<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t } from '$lib/stores/i18n';
    import { onMount } from 'svelte';
    import { currentLanguage, languagePreference } from '$lib/stores/languagePreference';
    import { ExpandOutline, DownloadOutline } from 'flowbite-svelte-icons';

    // Define API Base URL
    import { getBackendUrl } from '$src/lib/config';

    // Define props for startDate, endDate and selectedAgent
    export let startDate: string | undefined;
    export let endDate: string | undefined;
    export let selectedAgent: string = 'All Agents';
    export let agentNames: string[] = [];

    // Data fetched from backend
    let agentPerformanceMetrics: { agentName: string; responseTime: number; handlingTime: number; csatScore: number; }[] = [];
    let agentOverallPerformance: AgentOverallPerformanceItem[] = [];
    let ticketsTransferred: { agentName: string; amount: number; percentageChange: number; }[] = [];
    let ticketsReceived: { agentName: string; amount: number; percentageChange: number; }[] = [];
    let responseRate5Min: { agentName: string; responsePercentage: number; percentageChange: number; }[] = [];

    let overdueUnclosedTickets: UnclosedTicket[] = [];
    let overdueClosedTickets: ClosedTicket[] = [];
    
    // Loading and error states
    let isLoadingBarChart: boolean = true;
    let isLoadingTicketsTransferred: boolean = true;
    let isLoadingTicketsReceived: boolean = true;
    let isLoadingResponseRate: boolean = true;
    let isLoadingAgentOverallPerformance: boolean = true;

    let isLoadingOverdueUnclosedTickets: boolean = true;
    let isLoadingOverdueClosedTickets: boolean = true;

    // State for expanded chart/table modals
    let isBarChartExpanded: boolean = false;
    let isTicketsTransferredExpanded: boolean = false;
    let isTicketsReceivedExpanded: boolean = false;
    let isResponseRateExpanded: boolean = false;
    let isOverallPerformanceExpanded: boolean = false;

    let isOverdueUnclosedTicketsExpanded: boolean = false;
    let isOverdueClosedTicketsExpanded: boolean = false;

    // Define the interface for API response for tickets transferred/received/response rate
    interface TicketMetricAPIResponse {
        agent_id: number;
        agent_name: string;
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number;
            time_series_data: any[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number;
            time_series_data: any[];
        };
        percentage_change: number;
        units: string;
    }

    // Define the interface for Agent Overall Performance items
    interface AgentOverallPerformanceItem {
        agentName: string;
        amountOfClosedTickets: number;
        amountOfUnclosedTickets: number;
        averageResponseTime: number;
        averageHandlingTime: number;
        averageCsat: number; // out of 5
    }

    // For Closed and Unclosed Tickets

    interface UnclosedTicketsAPIResponse {
        ticket_number: string;
        status: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
        customer: string;
        priority: 'Immediately' | 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agent: string;
        created_time: string;
        closed_time: string | null;
        overdue_time: string;
    }

    interface ClosedTicketsAPIResponse {
        ticket_number: string;
        status: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
        customer: string;
        priority: 'Immediately' | 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agent: string;
        created_time: string;
        closed_time: string;
        overdue_time: string;
    }

    interface UnclosedTicket {
        ticketNo: string;
        ticketStatus: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
        customerName: string;
        priority: 'Immediately' | 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agentName: string;
        createdDateTime: string;
        currentDateTime: string;
        totalUsedTime: number;
    }

    interface ClosedTicket {
        ticketNo: string;
        ticketStatus: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed';
        customerName: string;
        priority: 'Immediately' | 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agentName: string;
        createdDateTime: string;
        closedDateTime: string;
        totalUsedTime: number;
        id: string;
    }

    // Sorting state and functionality
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        ticketsTransferred: { column: 'amount', direction: 'desc' },
        ticketsReceived: { column: 'amount', direction: 'desc' },
        responseRate5Min: { column: 'responsePercentage', direction: 'desc' },
        agentOverallPerformance: { column: 'amountOfClosedTickets', direction: 'desc' },
        overdueUnclosedTickets: { column: 'totalUsedTime', direction: 'desc' },
        overdueClosedTickets: { column: 'totalUsedTime', direction: 'desc' }        
    };

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            // Set initial sort direction based on key
            if (key === 'totalUsedTime' || key === 'count') {
                newDirection = 'desc';
            } else {
                newDirection = 'asc';
            }
        }

        currentSort = {
            ...currentSort,
            [tableName]: { column: String(key), direction: newDirection }
        };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            // Custom sort for 'priority' field
            if (key === 'priority') {
                const priorityOrder = { 'Immediately': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
                const aP = priorityOrder[aValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                const bP = priorityOrder[bValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                return newDirection === 'asc' ? aP - bP : bP - aP;
            }

            // Custom sort for 'sentiment' field
            if (key === 'sentiment') {
                const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                return newDirection === 'asc' ? aS - bS : bS - aS;
            }

            // Sort for date-time strings
            if (['createdDateTime', 'currentDateTime', 'closedDateTime'].includes(String(key))) {
                const valA = new Date(aValue as string).getTime();
                const valB = new Date(bValue as string).getTime();
                return newDirection === 'asc' ? valA - valB : valB - valA;
            } 
            
            // General sort for numbers
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            } 
            
            // General sort for strings
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            }

            // Return 0 if types are incompatible or unknown
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;

                // Custom sort for 'priority' field
                if (key === 'priority') {
                    const priorityOrder = { 'Immediately': 4, 'High': 3, 'Medium': 2, 'Low': 1 };
                    const aP = priorityOrder[aValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                    const bP = priorityOrder[bValue as 'Immediately' | 'High' | 'Medium' | 'Low'];
                    return direction === 'asc' ? aP - bP : bP - aP;
                }

                // Custom sort for 'sentiment' field
                if (key === 'sentiment') {
                    const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                    const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                    const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                    return direction === 'asc' ? aS - bS : bS - aS;
                }

                // Sort for date-time strings
                if (['createdDateTime', 'currentDateTime', 'closedDateTime'].includes(String(key))) {
                    const valA = new Date(aValue as string).getTime();
                    const valB = new Date(bValue as string).getTime();
                    return direction === 'asc' ? valA - valB : valB - valA;
                } 
                
                // General sort for numbers
                if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                } 
                
                // General sort for strings
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }

                // Return 0 if types are incompatible or unknown
                return 0;
            });
        }
        return dataArray;
    }

    function formatDateTimeForDisplay(isoString: string | null): string {
        if (!isoString) return 'N/A';
        try {
            const date = new Date(isoString);
            const lang = $currentLanguage;
            
            return date.toLocaleDateString(lang, {
                day: 'numeric',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
        } catch (e) {
            console.error("Error formatting date-time:", e);
            return isoString;
        }
    }

    function parseApiOverdueTime(apiTimeStr: string | null): number {
        if (!apiTimeStr) return 0;

        const parts = apiTimeStr.split(' ');
        if (parts.length !== 2) {
            console.warn(`Unexpected overdue_time format: ${apiTimeStr}`);
            return 0;
        }

        const days = parseInt(parts[0], 10);
        const timeParts = parts[1].split(':');

        if (timeParts.length !== 3) {
            console.warn(`Unexpected time part format in overdue_time: ${apiTimeStr}`);
            return 0;
        }

        const hours = parseInt(timeParts[0], 10);
        const minutes = parseInt(timeParts[1], 10);
        const seconds = parseFloat(timeParts[2]);

        return days * 24 * 3600 + hours * 3600 + minutes * 60 + seconds;
    }

    function formatOverdueTime(seconds: number): string {
        const days = Math.floor(seconds / (3600 * 24));
        const hours = Math.floor((seconds % (3600 * 24)) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        let parts = [];
        if (days > 0) parts.push(`${days}${t('db.timeUnitDay')}`);
        if (hours > 0) parts.push(`${hours}${t('db.timeUnitHour')}`);
        if (minutes > 0) parts.push(`${minutes}${t('db.timeUnitMinute')}`);

        if (parts.length === 0) return `0${t('db.timeUnitMinute')}`;
        return parts.join(' ');
    }

    // ADDED: Helper function to filter data based on selected agent.
    function filterDataByAgent<T extends { agent_name?: string; agent?: string }>(data: T[], selectedAgent: string, agentNames: string[]): T[] {
        if (selectedAgent && selectedAgent !== 'All Agents' && agentNames.includes(selectedAgent)) {
            // Check for both possible agent key names
            return data.filter(item => (item.agent_name === selectedAgent) || (item.agent === selectedAgent));
        }
        return data;
    }

    // Add initialization tracking
    let hasInitialized = false;
    let isInitializing = true;

    // Track previous values to detect actual changes
    let previousAgent: string | undefined = undefined;
    let previousStartDate: string | undefined = undefined;
    let previousEndDate: string | undefined = undefined;
    let previousLanguage: string | undefined = undefined;

    async function fetchData() {
        console.log("fetchData called for AgentPerformanceTab");
        // Use props directly
        console.log(`Current filter settings (from props): selectedAgent='${selectedAgent}', startDate='${startDate}', endDate='${endDate}'`);

        // Reset loading states for all components
        isLoadingBarChart = true;
        isLoadingTicketsTransferred = true;
        isLoadingTicketsReceived = true;
        isLoadingResponseRate = true;
        isLoadingAgentOverallPerformance = true;
        isLoadingOverdueUnclosedTickets = true;
        isLoadingOverdueClosedTickets = true;
        
        // Build query params using URLSearchParams
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Only add lang=th if language is Thai
        const currentLang = languagePreference.getCurrentLanguage();
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const queryString = urlParams.toString();
        const baseUrl = getBackendUrl();

        // Define all fetch promises
        const endpoints = [
            'agent-performance-summary',
            'agent-previous-assignment-count',
            'agent-assigned-tickets-count',
            'agent-response-rate-within-5min',
            'comprehensive-agent-performance',
            'overdue-unclosed-tickets',
            'overdue-closed-tickets'
        ];

        const requests = endpoints.map(endpoint =>
            fetch(`${baseUrl}/dashboard/api/${endpoint}/?${queryString}`)
        );

        const [
            summaryResult,
            ticketsTransferredResult,
            ticketsReceivedResult,
            responseRateResult,
            overallPerformanceResult,
            overdueUnclosedTicketsResult,
            overdueClosedTicketsResult  
        ] = await Promise.allSettled(requests);                

        // Process All Agent Names for Filter & Overall Performance Table
        if (overallPerformanceResult.status === 'fulfilled' && overallPerformanceResult.value.ok) {
            try {
                const dataOverallPerformance = await overallPerformanceResult.value.json();
                if (Array.isArray(dataOverallPerformance) && dataOverallPerformance.length > 0) {
                    // First, populate the agentNames filter from the comprehensive data
                    agentNames = Array.from(new Set(dataOverallPerformance.map((item: any) => item.agent_name)));
                    agentNames.sort();
                    console.log("Agent names for filter populated:", agentNames);

                    // Now, use the same data to populate the overall performance table, with filtering
                    const filteredDataOverallPerformance = filterDataByAgent(dataOverallPerformance, selectedAgent, agentNames);

                    const processedOverallPerformance = filteredDataOverallPerformance.map((item: any) => ({
                        agentName: item.agent_name,
                        amountOfClosedTickets: item.closed_tickets ?? 0,
                        amountOfUnclosedTickets: item.unclosed_tickets ?? 0,
                        averageResponseTime: item.avg_response_time_minutes ?? 0,
                        averageHandlingTime: item.avg_handling_time_minutes ?? 0,
                        averageCsat: item.avg_csat_score ?? 0,
                    }));
                    agentOverallPerformance = applyInitialSort(processedOverallPerformance, 'agentOverallPerformance');
                } else {
                    console.warn("Fetched agent overall performance data is not an array or is empty.");
                    agentNames = [];
                    agentOverallPerformance = [];
                }
            } catch (error) {
                console.error('Error parsing agent overall performance data:', error);
                agentNames = [];
                agentOverallPerformance = [];
            }
        } else {
            console.error('Failed to fetch agent overall performance data:', overallPerformanceResult.status === 'rejected' ? overallPerformanceResult.reason : `HTTP error: ${overallPerformanceResult.value?.status}`);
            agentNames = [];
            agentOverallPerformance = [];
        }
        isLoadingAgentOverallPerformance = false;

        // Process Agent Performance Summary (Bar Chart)
        if (summaryResult.status === 'fulfilled' && summaryResult.value.ok) {
            try {
                const dataSummary = await summaryResult.value.json();
                if (Array.isArray(dataSummary) && dataSummary.length > 0) {
                    // Use the helper function for filtering.
                    const filteredDataSummary = filterDataByAgent(dataSummary, selectedAgent, agentNames);
                    agentPerformanceMetrics = filteredDataSummary.map((item: any) => ({
                        agentName: item.agent_name,
                        responseTime: item.avg_response_time_minutes ?? 0,
                        handlingTime: item.avg_handling_time_minutes ?? 0,
                        csatScore: item.average_csat_score ?? 0
                    }));
                } else {
                    console.warn("Fetched summary data is not an array or is empty.");
                    agentPerformanceMetrics = [];
                }
            } catch (error) {
                console.error('Error parsing agent performance summary (bar chart) data:', error);
                agentPerformanceMetrics = [];
            }
        } else {
            console.error('Failed to fetch agent performance summary (bar chart):', summaryResult.status === 'rejected' ? summaryResult.reason : `HTTP error: ${summaryResult.value?.status}`);
            agentPerformanceMetrics = [];
        }
        isLoadingBarChart = false;

        // Process Tickets Transferred to Others Table
        if (ticketsTransferredResult.status === 'fulfilled' && ticketsTransferredResult.value.ok) {
            try {
                const dataTicketsTransferred: TicketMetricAPIResponse[] = await ticketsTransferredResult.value.json();
                if (Array.isArray(dataTicketsTransferred) && dataTicketsTransferred.length > 0) {
                    // Use the helper function for filtering.
                    const filteredDataTicketsTransferred = filterDataByAgent(dataTicketsTransferred, selectedAgent, agentNames);
                    const processedTicketsTransferred = filteredDataTicketsTransferred.map((item: TicketMetricAPIResponse) => ({
                        agentName: item.agent_name,
                        amount: item.main_period.metric_value ?? 0,
                        percentageChange: item.percentage_change ?? 0
                    }));
                    ticketsTransferred = applyInitialSort(processedTicketsTransferred, 'ticketsTransferred');
                } else {
                    console.warn("Fetched tickets transferred data is not an array or is empty.");
                    ticketsTransferred = [];
                }
            } catch (error) {
                console.error('Error parsing tickets transferred data:', error);
                ticketsTransferred = [];
            }
        } else {
            console.error('Failed to fetch tickets transferred data:', ticketsTransferredResult.status === 'rejected' ? ticketsTransferredResult.reason : `HTTP error: ${ticketsTransferredResult.value?.status}`);
            ticketsTransferred = [];
        }
        isLoadingTicketsTransferred = false;

        // Process Tickets Received From Others Table
        if (ticketsReceivedResult.status === 'fulfilled' && ticketsReceivedResult.value.ok) {
            try {
                const dataTicketsReceived: TicketMetricAPIResponse[] = await ticketsReceivedResult.value.json();
                if (Array.isArray(dataTicketsReceived) && dataTicketsReceived.length > 0) {
                    // Use the helper function for filtering.
                    const filteredDataTicketsReceived = filterDataByAgent(dataTicketsReceived, selectedAgent, agentNames);
                    const processedTicketsReceived = filteredDataTicketsReceived.map((item: TicketMetricAPIResponse) => ({
                        agentName: item.agent_name,
                        amount: item.main_period.metric_value ?? 0,
                        percentageChange: item.percentage_change ?? 0
                    }));
                    ticketsReceived = applyInitialSort(processedTicketsReceived, 'ticketsReceived');
                } else {
                    console.warn("Fetched tickets received data is not an array or is empty.");
                    ticketsReceived = [];
                }
            } catch (error) {
                console.error('Error parsing tickets received data:', error);
                ticketsReceived = [];
            }
        } else {
            console.error('Failed to fetch tickets received data:', ticketsReceivedResult.status === 'rejected' ? ticketsReceivedResult.reason : `HTTP error: ${ticketsReceivedResult.value?.status}`);
            ticketsReceived = [];
        }
        isLoadingTicketsReceived = false;

        // REVISED: Process Response Rate in 5 Mins Table
        if (responseRateResult.status === 'fulfilled' && responseRateResult.value.ok) {
            try {
                const dataResponseRate: TicketMetricAPIResponse[] = await responseRateResult.value.json();
                if (Array.isArray(dataResponseRate) && dataResponseRate.length > 0) {
                    // Use the helper function for filtering.
                    const filteredDataResponseRate = filterDataByAgent(dataResponseRate, selectedAgent, agentNames);
                    const processedResponseRate = filteredDataResponseRate.map((item: TicketMetricAPIResponse) => ({
                        agentName: item.agent_name,
                        responsePercentage: item.main_period.metric_value ?? 0,
                        percentageChange: item.percentage_change ?? 0
                    }));
                    responseRate5Min = applyInitialSort(processedResponseRate, 'responseRate5Min');
                } else {
                    console.warn("Fetched response rate data is not an array or is empty.");
                    responseRate5Min = [];
                }
            } catch (error) {
                console.error('Error parsing response rate data:', error);
                responseRate5Min = [];
            }
        } else {
            console.error('Failed to fetch response rate data:', responseRateResult.status === 'rejected' ? responseRateResult.reason : `HTTP error: ${responseRateResult.value?.status}`);
            responseRate5Min = [];
        }
        isLoadingResponseRate = false;

        // Process Overdue Unclosed Tickets Table
        if (overdueUnclosedTicketsResult.status === 'fulfilled' && overdueUnclosedTicketsResult.value.ok) {
            try {
                const data: UnclosedTicketsAPIResponse[] = await overdueUnclosedTicketsResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    // REVISED: Apply agent filter here
                    const filteredData = filterDataByAgent(data, selectedAgent, agentNames);
                    overdueUnclosedTickets = applyInitialSort(filteredData.map(item => ({
                        ticketNo: item.ticket_number,
                        ticketStatus: item.status,
                        customerName: item.customer,
                        priority: item.priority,
                        sentiment: item.sentiment,
                        agentName: item.agent,
                        totalUsedTime: parseApiOverdueTime(item.overdue_time),
                        createdDateTime: item.created_time,
                        currentDateTime: new Date().toISOString()
                    })), 'overdueUnclosedTickets');
                } else {
                    overdueUnclosedTickets = [];
                }
            } catch (error) {
                console.error('Error parsing overdue unclosed tickets data:', error);
                overdueUnclosedTickets = [];
            }
        } else {
            console.error('Failed to fetch overdue unclosed tickets data:', overdueUnclosedTicketsResult.status === 'rejected' ? overdueUnclosedTicketsResult.reason : `HTTP error: ${overdueUnclosedTicketsResult.value?.status}`);
            overdueUnclosedTickets = [];
        }
        isLoadingOverdueUnclosedTickets = false;

        // Process Overdue Closed Tickets Table
        if (overdueClosedTicketsResult.status === 'fulfilled' && overdueClosedTicketsResult.value.ok) {
            try {
                const data: ClosedTicketsAPIResponse[] = await overdueClosedTicketsResult.value.json();
                if (Array.isArray(data) && data.length > 0) {
                    // REVISED: Apply agent filter here
                    const filteredData = filterDataByAgent(data, selectedAgent, agentNames);
                    overdueClosedTickets = applyInitialSort(filteredData.map(item => ({
                        ticketNo: item.ticket_number,
                        ticketStatus: item.status,
                        customerName: item.customer,
                        priority: item.priority,
                        sentiment: item.sentiment,
                        agentName: item.agent,
                        totalUsedTime: parseApiOverdueTime(item.overdue_time),
                        createdDateTime: item.created_time,
                        closedDateTime: item.closed_time,
                        id: `${item.ticket_number}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
                    })), 'overdueClosedTickets');
                } else {
                    overdueClosedTickets = [];
                }
            } catch (error) {
                console.error('Error parsing overdue closed tickets data:', error);
                overdueClosedTickets = [];
            }
        } else {
            console.error('Failed to fetch overdue closed tickets data:', overdueClosedTicketsResult.status === 'rejected' ? overdueClosedTicketsResult.reason : `HTTP error: ${overdueClosedTicketsResult.value?.status}`);
            overdueClosedTickets = [];
        }
        isLoadingOverdueClosedTickets = false;

    }

    // Functions to add for downloading Excel files
    async function downloadAgentPerformanceSummaryExcel() {
        console.log("Attempting to download agent performance summary excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/agent-performance-summary.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.individualPerformanceExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadTicketsTransferredExcel() {
        console.log("Attempting to download tickets transferred excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/agent-previous-assignment-count.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.ticketsTransferredExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }    

    async function downloadTicketsReceivedExcel() {
        console.log("Attempting to download tickets received excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/agent-assigned-tickets-count.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.ticketsReceivedExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadResponseRate5MinExcel() {
        console.log("Attempting to download response rate 5 mins excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/agent-response-rate-within-5min.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.responseRate5MinExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadAgentOverallPerformanceSummaryExcel() {
        console.log("Attempting to download agent overall performance summary excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/comprehensive-agent-performance.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.agentOverallPerformanceSummaryExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadUnclosedTicketsOver1DayExcel() {
        console.log("Attempting to download unclosed tickets over 1 day excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/overdue-unclosed-tickets.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.unclosedTicketsOver1DayExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }

    async function downloadClosedTicketsOver1DayExcel() {
        console.log("Attempting to download closed tickets over 1 day excel file.");

        const currentLang = languagePreference.getCurrentLanguage();
        const urlParams = new URLSearchParams();

        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Append language param only if Thai
        if (currentLang === 'th') {
            urlParams.append('lang', 'th');
        }

        const query = urlParams.toString();
        const downloadUrl = `${getBackendUrl()}/dashboard/api/overdue-closed-tickets.xlsx/?${query}`;
        console.log("Download URL:", downloadUrl);

        try {
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let filename;

            // Use backend filename only if current language is Thai
            if (currentLang === 'th') {
                const contentDisposition = response.headers.get('Content-Disposition');
                const filenameMatch = contentDisposition?.match(/filename="(.+?)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Fallback to frontend translation-based filename
            filename ??= `${t('dbAgent.closedTicketsOver1DayExcel')}_${startDate}_${endDate}.xlsx`;

            // Create blob and trigger download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Download failed:', error);
            alert(t('db.excelAlert'));
        }
    }
        
    onMount(() => {
        console.log("Component mounted: initiating fetchData");
        isInitializing = true;

        // Set initial values to track changes
        previousAgent = selectedAgent;
        previousStartDate = startDate;
        previousEndDate = endDate;
        previousLanguage = $currentLanguage;
        
        fetchData().finally(() => {
            hasInitialized = true;
            isInitializing = false;
        });
    });

    // Only trigger fetchData after initialization is complete and values actually change
    $: if (hasInitialized && !isInitializing) {
        const currentLanguage = $currentLanguage;
        
        // Check if any values have actually changed
        const hasAgentChanged = selectedAgent !== previousAgent;
        const hasStartDateChanged = startDate !== previousStartDate;
        const hasEndDateChanged = endDate !== previousEndDate;
        const hasLanguageChanged = currentLanguage !== previousLanguage;
        
        if (hasAgentChanged || hasStartDateChanged || hasEndDateChanged || hasLanguageChanged) {
            console.log("Filter values changed, triggering fetchData:", {
                agent: { old: previousAgent, new: selectedAgent },
                startDate: { old: previousStartDate, new: startDate },
                endDate: { old: previousEndDate, new: endDate },
                language: { old: previousLanguage, new: currentLanguage }
            });
            
            // Update tracking variables
            previousAgent = selectedAgent;
            previousStartDate = startDate;
            previousEndDate = endDate;
            previousLanguage = currentLanguage;
            
            fetchData();
        }
    }

    function getPriorityColor(priority: 'Immediately' | 'High' | 'Medium' | 'Low'): string {
        switch (priority) {
            case 'Immediately': return 'bg-red-100 text-red-800';
            case 'High': return 'bg-orange-100 text-orange-800';
            case 'Medium': return 'bg-yellow-100 text-yellow-800';
            case 'Low': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function getSentimentColor(sentiment: 'Positive' | 'Neutral' | 'Negative'): string {
        switch (sentiment) {
            case 'Positive': return 'bg-green-100 text-green-800';
            case 'Neutral': return 'bg-gray-100 text-gray-800';
            case 'Negative': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function getStatusColor(priority: 'open' | 'assigned' | 'waiting' | 'pending_to_close' | 'closed'): string {
        switch (priority) {
            case 'open': return 'bg-green-100 text-green-800';
            case 'assigned': return 'bg-blue-100 text-blue-800';
            case 'waiting': return 'bg-yellow-100 text-yellow-800';
            case 'pending_to_close': return 'bg-gray-100 text-gray-800';
            case 'closed': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

</script>

{#if isOverdueUnclosedTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.unclosedTicketsOver1Day')}</h3>
                <button on:click={() => isOverdueUnclosedTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingOverdueUnclosedTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if overdueUnclosedTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketNo', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'ticketNo'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketStatus', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'ticketStatus'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'customerName', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'customerName'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'priority', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'priority'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'sentiment', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'sentiment'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'agentName', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'agentName'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'totalUsedTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'totalUsedTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>                                
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'createdDateTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'createdDateTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'currentDateTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.currentTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'currentDateTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each overdueUnclosedTickets as item (item.ticketNo)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full {getStatusColor(item.ticketStatus)}">
                                            {t("tickets_" + item.ticketStatus)}
                                        </span>
                                    </td>                                    
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full {getPriorityColor(item.priority)}">
                                            {t("tickets_priority_" + item.priority.toLowerCase())}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-medium rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment ? item.sentiment : "-"}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.currentDateTime)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverdueClosedTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.closedTicketsOver1Day')}</h3>
                <button on:click={() => isOverdueClosedTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingOverdueClosedTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if overdueClosedTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketNo', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.overdueClosedTickets.column === 'ticketNo'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketStatus', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.overdueClosedTickets.column === 'ticketStatus'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'customerName', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.overdueClosedTickets.column === 'customerName'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'priority', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.overdueClosedTickets.column === 'priority'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'sentiment', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.overdueClosedTickets.column === 'sentiment'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'agentName', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.overdueClosedTickets.column === 'agentName'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'totalUsedTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'totalUsedTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>                                
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'createdDateTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'createdDateTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'closedDateTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.closedTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'closedDateTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each overdueClosedTickets as item (item.id)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                            {t("tickets_" + item.ticketStatus)}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                            {t("tickets_priority_" + item.priority.toLowerCase())}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment ? item.sentiment : "-"}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isBarChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.individualPerformance')}</h3>
                <button on:click={() => isBarChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full">
                {#if isLoadingBarChart}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                    </div>
                {:else if agentPerformanceMetrics.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={agentPerformanceMetrics}
                        chartType="groupedBar"
                        labelKey="agentName"
                        groupedKeys={[
                            { key: 'responseTime', label: t('dbAgent.avgResponseTimeSeconds'), color: COLORS.darkBlue },
                            { key: 'handlingTime', label: t('dbAgent.avgHandlingTimeMinutes'), color: COLORS.blue },
                        ]}
                        showRightYAxis={true}
                        rightYAxisKey="csatScore"
                        rightYAxisLabel={t('dbAgent.avgCsatScoreOutOf5')}
                        rightYAxisColor={COLORS.sky}
                        rightYAxisMin={0}
                        rightYAxisMax={5}
                        label=""
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isTicketsTransferredExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.ticketsTransferred')}</h3>
                <button on:click={() => isTicketsTransferredExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingTicketsTransferred}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsTransferred.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'agentName', 'ticketsTransferred')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsTransferred.column === 'agentName'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'amount', 'ticketsTransferred')}>
                                    {t('dbAgent.amountTransferred')}
                                    {#if currentSort.ticketsTransferred.column === 'amount'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'percentageChange', 'ticketsTransferred')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsTransferred.column === 'percentageChange'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsTransferred as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>                        
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isTicketsReceivedExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.ticketsReceived')}</h3>
                <button on:click={() => isTicketsReceivedExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingTicketsReceived}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsReceived.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'agentName', 'ticketsReceived')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsReceived.column === 'agentName'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'amount', 'ticketsReceived')}>
                                    {t('dbAgent.amountReceived')}
                                    {#if currentSort.ticketsReceived.column === 'amount'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'percentageChange', 'ticketsReceived')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsReceived.column === 'percentageChange'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsReceived as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isResponseRateExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.responseRate5Min')}</h3>
                <button on:click={() => isResponseRateExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingResponseRate}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if responseRate5Min.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'agentName', 'responseRate5Min')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.responseRate5Min.column === 'agentName'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'responsePercentage', 'responseRate5Min')}>
                                    {t('dbAgent.percentageResponse5Min')}
                                    {#if currentSort.responseRate5Min.column === 'responsePercentage'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'percentageChange', 'responseRate5Min')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.responseRate5Min.column === 'percentageChange'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each responseRate5Min as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.responsePercentage.toFixed(1)}%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverallPerformanceExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbAgent.agentOverallPerformanceSummary')}</h3>
                <button on:click={() => isOverallPerformanceExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingAgentOverallPerformance}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if agentOverallPerformance.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'agentName', 'agentOverallPerformance')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.agentOverallPerformance.column === 'agentName'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfClosedTickets', 'agentOverallPerformance')}>
                                    {t('dbAgent.amountOfClosedTickets')}
                                    {#if currentSort.agentOverallPerformance.column === 'amountOfClosedTickets'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfUnclosedTickets', 'agentOverallPerformance')}>
                                    {t('dbAgent.amountOfUnclosedTickets')}
                                    {#if currentSort.agentOverallPerformance.column === 'amountOfUnclosedTickets'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageResponseTime', 'agentOverallPerformance')}>
                                    {t('dbAgent.avgResponseTime')}
                                    {#if currentSort.agentOverallPerformance.column === 'averageResponseTime'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageHandlingTime', 'agentOverallPerformance')}>
                                    {t('dbAgent.avgHandlingTime')}
                                    {#if currentSort.agentOverallPerformance.column === 'averageHandlingTime'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageCsat', 'agentOverallPerformance')}>
                                    {t('dbAgent.avgCsat')}
                                    {#if currentSort.agentOverallPerformance.column === 'averageCsat'}
                                        {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each agentOverallPerformance as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-center">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfClosedTickets}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfUnclosedTickets}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageResponseTime.toFixed(1)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageHandlingTime.toFixed(1)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageCsat.toFixed(1)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-2">
    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.unclosedTicketsOver1Day')}</h2>
            <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                <button 
                    on:click={() => isOverdueUnclosedTicketsExpanded = true}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.expand')} -->
                    <ExpandOutline class="h-5 w-5" />
                </button>
                <button 
                    on:click={downloadUnclosedTicketsOver1DayExcel}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.downloadExcel')} -->
                    <DownloadOutline class="h-5 w-5" />
                </button>
            </div>
        </div>
        <div class="overflow-y-auto max-h-96">
            {#if isLoadingOverdueUnclosedTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if overdueUnclosedTickets.length === 0}
                <div class="text-gray-600 text-center text-lg py-0">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketNo', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.overdueUnclosedTickets.column === 'ticketNo'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketStatus', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.overdueUnclosedTickets.column === 'ticketStatus'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'customerName', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.overdueUnclosedTickets.column === 'customerName'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'priority', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.overdueUnclosedTickets.column === 'priority'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'sentiment', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.overdueUnclosedTickets.column === 'sentiment'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'agentName', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.overdueUnclosedTickets.column === 'agentName'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'totalUsedTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'totalUsedTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>                            
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'createdDateTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'createdDateTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'currentDateTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.currentTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'currentDateTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each overdueUnclosedTickets as item (item.ticketNo)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                        {t("tickets_" + item.ticketStatus)}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {t("tickets_priority_" + item.priority.toLowerCase())}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment ? item.sentiment : "-"}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.currentDateTime)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.closedTicketsOver1Day')}</h2>
            <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                <button 
                    on:click={() => isOverdueClosedTicketsExpanded = true}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.expand')} -->
                    <ExpandOutline class="h-5 w-5" />
                </button>
                <button 
                    on:click={downloadClosedTicketsOver1DayExcel}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.downloadExcel')} -->
                    <DownloadOutline class="h-5 w-5" />
                </button>
            </div>
        </div>
        <div class="overflow-y-auto max-h-96">
            {#if isLoadingOverdueClosedTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if overdueClosedTickets.length === 0}
                <div class="text-gray-600 text-center text-lg py-0">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketNo', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.overdueClosedTickets.column === 'ticketNo'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketStatus', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.overdueClosedTickets.column === 'ticketStatus'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'customerName', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.overdueClosedTickets.column === 'customerName'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'priority', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.overdueClosedTickets.column === 'priority'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'sentiment', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.overdueClosedTickets.column === 'sentiment'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'agentName', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.overdueClosedTickets.column === 'agentName'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'totalUsedTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.overdueClosedTickets.column === 'totalUsedTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>                            
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'createdDateTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.overdueClosedTickets.column === 'createdDateTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'closedDateTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.closedTime')}
                                {#if currentSort.overdueClosedTickets.column === 'closedDateTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each overdueClosedTickets as item (item.id)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getStatusColor(item.ticketStatus)}">
                                        {t("tickets_" + item.ticketStatus)}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {t("tickets_priority_" + item.priority.toLowerCase())}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment ? item.sentiment : "-"}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.individualPerformance')}</h2>
            <div class="flex gap-2">
                <button 
                    on:click={() => isBarChartExpanded = true}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.expand')} -->
                    <ExpandOutline class="h-5 w-5" />
                </button>
                <button 
                    on:click={downloadAgentPerformanceSummaryExcel}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.downloadExcel')} -->
                    <DownloadOutline class="h-5 w-5" />
                </button>
            </div>
        </div>
        
        <div 
            class="w-full flex items-center justify-center transition-all duration-300 ease-in-out" 
            class:h-96={agentPerformanceMetrics.length > 0} 
            class:min-h-[10rem]={agentPerformanceMetrics.length === 0}
        >
            {#if isLoadingBarChart}
                <div class="flex flex-col items-center justify-center text-gray-600 py-12">
                    <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-lg">{t('db.loadingData')}</p>
                </div>
            {:else if agentPerformanceMetrics.length === 0}
                <div class="text-gray-600 text-center text-lg py-12">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                {@const filteredBarChartData = agentPerformanceMetrics.filter(item => 
                    item.responseTime > 0 || item.handlingTime > 0 || item.csatScore > 0
                )}
                
                {#if filteredBarChartData.length === 0}
                    <div class="text-gray-600 text-center text-lg py-12">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={filteredBarChartData}
                        chartType="groupedBar"
                        labelKey="agentName"
                        groupedKeys={[
                            { key: 'responseTime', label: t('dbAgent.avgResponseTimeSeconds'), color: COLORS.darkBlue },
                            { key: 'handlingTime', label: t('dbAgent.avgHandlingTimeMinutes'), color: COLORS.blue },
                        ]}
                        showRightYAxis={true}
                        rightYAxisKey="csatScore"
                        rightYAxisLabel={t('dbAgent.avgCsatScoreOutOf5')}
                        rightYAxisColor={COLORS.sky}
                        rightYAxisMin={0}
                        rightYAxisMax={5}
                        label=""
                        showValueLabels={true}
                    />
                {/if}
            {/if}
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.ticketsTransferred')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isTicketsTransferredExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadTicketsTransferredExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto max-h-96">
                {#if isLoadingTicketsTransferred}
                    <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsTransferred.length === 0}
                    <div class="text-gray-600 text-center text-lg py-0">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'agentName', 'ticketsTransferred')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsTransferred.column === 'agentName'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'amount', 'ticketsTransferred')}>
                                    {t('dbAgent.amountTransferred')}
                                    {#if currentSort.ticketsTransferred.column === 'amount'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsTransferred = sortTable(ticketsTransferred, 'percentageChange', 'ticketsTransferred')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsTransferred.column === 'percentageChange'}
                                        {#if currentSort.ticketsTransferred.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsTransferred as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.ticketsReceived')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isTicketsReceivedExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadTicketsReceivedExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto max-h-96">
                {#if isLoadingTicketsReceived}
                    <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if ticketsReceived.length === 0}
                    <div class="text-gray-600 text-center text-lg py-0">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'agentName', 'ticketsReceived')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.ticketsReceived.column === 'agentName'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'amount', 'ticketsReceived')}>
                                    {t('dbAgent.amountReceived')}
                                    {#if currentSort.ticketsReceived.column === 'amount'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => ticketsReceived = sortTable(ticketsReceived, 'percentageChange', 'ticketsReceived')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.ticketsReceived.column === 'percentageChange'}
                                        {#if currentSort.ticketsReceived.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each ticketsReceived as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amount}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-red-600' : item.percentageChange < 0 ? 'text-green-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.responseRate5Min')}</h2>
                <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                    <button 
                        on:click={() => isResponseRateExpanded = true}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.expand')} -->
                        <ExpandOutline class="h-5 w-5" />
                    </button>
                    <button 
                        on:click={downloadResponseRate5MinExcel}
                        class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        <!-- {t('db.downloadExcel')} -->
                        <DownloadOutline class="h-5 w-5" />
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto max-h-96">
                {#if isLoadingResponseRate}
                    <div class="flex flex-col items-center justify-center text-gray-600 py-0">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if responseRate5Min.length === 0}
                    <div class="text-gray-600 text-center text-lg py-0">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'agentName', 'responseRate5Min')}>
                                    {t('dbAgent.agent')}
                                    {#if currentSort.responseRate5Min.column === 'agentName'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'responsePercentage', 'responseRate5Min')}>
                                    {t('dbAgent.percentageResponse5Min')}
                                    {#if currentSort.responseRate5Min.column === 'responsePercentage'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => responseRate5Min = sortTable(responseRate5Min, 'percentageChange', 'responseRate5Min')}>
                                    {t('dbAgent.percentageComparedToPreviousPeriod')}
                                    {#if currentSort.responseRate5Min.column === 'percentageChange'}
                                        {#if currentSort.responseRate5Min.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each responseRate5Min as item}
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.responsePercentage.toFixed(1)}%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                        <span class="{item.percentageChange > 0 ? 'text-green-600' : item.percentageChange < 0 ? 'text-red-600' : 'text-gray-500'} flex items-center justify-center">
                                            {#if item.percentageChange > 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                                </svg>
                                            {:else if item.percentageChange < 0}
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                                </svg>
                                            {/if}
                                            {item.percentageChange > 0 ? '+' : ''}{item.percentageChange.toFixed(1)}%
                                        </span>
                                    </td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbAgent.agentOverallPerformanceSummary')}</h2>
            <div class="flex gap-2 flex-wrap sm:flex-nowrap">
                <button 
                    on:click={() => isOverallPerformanceExpanded = true}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.expand')} -->
                    <ExpandOutline class="h-5 w-5" />
                </button>
                <button
                    on:click={downloadAgentOverallPerformanceSummaryExcel}
                    class="bg-white text-blue-700 border border-blue-500 font-semibold py-2 px-2 rounded-md text-sm transition duration-200 ease-in-out hover:bg-blue-50 hover:border-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    <!-- {t('db.downloadExcel')} -->
                    <DownloadOutline class="h-5 w-5" />
                </button>
            </div>
        </div>
        <div class="overflow-x-auto overflow-y-auto max-h-96">
            {#if isLoadingAgentOverallPerformance}
                <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if agentOverallPerformance.length === 0}
                <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'agentName', 'agentOverallPerformance')}>
                                {t('dbAgent.agent')}
                                {#if currentSort.agentOverallPerformance.column === 'agentName'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfClosedTickets', 'agentOverallPerformance')}>
                                {t('dbAgent.amountOfClosedTickets')}
                                {#if currentSort.agentOverallPerformance.column === 'amountOfClosedTickets'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'amountOfUnclosedTickets', 'agentOverallPerformance')}>
                                {t('dbAgent.amountOfUnclosedTickets')}
                                {#if currentSort.agentOverallPerformance.column === 'amountOfUnclosedTickets'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageResponseTime', 'agentOverallPerformance')}>
                                {t('dbAgent.avgResponseTime')}
                                {#if currentSort.agentOverallPerformance.column === 'averageResponseTime'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageHandlingTime', 'agentOverallPerformance')}>
                                {t('dbAgent.avgHandlingTime')}
                                {#if currentSort.agentOverallPerformance.column === 'averageHandlingTime'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => agentOverallPerformance = sortTable(agentOverallPerformance, 'averageCsat', 'agentOverallPerformance')}>
                                {t('dbAgent.avgCsat')}
                                {#if currentSort.agentOverallPerformance.column === 'averageCsat'}
                                    {#if currentSort.agentOverallPerformance.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each agentOverallPerformance as item}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-left">{item.agentName}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfClosedTickets}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.amountOfUnclosedTickets}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageResponseTime.toFixed(1)}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageHandlingTime.toFixed(1)}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">{item.averageCsat.toFixed(1)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>
</div>