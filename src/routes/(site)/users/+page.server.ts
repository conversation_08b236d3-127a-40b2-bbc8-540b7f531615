// +page.server.ts
import type { PageServerLoad } from './$types';
import { services } from '$lib/api/features';
import { error, fail, redirect } from '@sveltejs/kit';
import type { Actions } from './$types';

export const load: PageServerLoad = async ({ cookies }) => {
	let access_token = cookies.get('access_token');
	let refresh_token = cookies.get('refresh_token');
	if (!access_token) {
		return {
			all_users: [],
			statuses: [],
			roles: [],
			partners: [],
			all_tags: [],
			error: 'No access token available'
		};
	}

	for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
		try {
			// Remove users.getAll() - let client handle all user fetching with pagination
			const user_reponse = await services.users.getAll(access_token);

			// Get filter data from new endpoints
			const filter_partners_response = await services.users.getFilterPartners(access_token);
			const filter_departments_response = await services.users.getFilterDepartments(access_token);
			const filter_tags_response = await services.users.getFilterSpecializedTags(access_token);

			if (
				user_reponse.res_status === 401 ||
				filter_partners_response.res_status === 401 ||
				filter_departments_response.res_status === 401 ||
				filter_tags_response.res_status === 401
			) {
				throw error(401, 'Invalid access token!!!');
			}

			// console.log(user_reponse.users)

			return {
				all_users: user_reponse.users || [], //[], // Start with empty array - client will fetch paginated users
				partners: filter_partners_response.partners || [],
				departments: filter_departments_response.departments || [],
				all_tags: filter_tags_response.specialized_tags || [],
				token: access_token
			};
		} catch (err) {
			// console.error('Error fetching user details:', err);
			// error(500, 'Failed to load user details');

			const refreshResponse = await services.users.refreshToken(refresh_token);
			const login_token = refreshResponse.login_token;

			if (login_token.length === 0) {
				cookies.set('isLogin', 'false', { path: '/' });
				throw redirect(302, '/login');
			} else {
				access_token = login_token.access;
				refresh_token = login_token.refresh;

				cookies.set('access_token', access_token, { path: '/' });
				cookies.set('refresh_token', refresh_token, { path: '/' });
			}
		}
	}
};

export const actions: Actions = {
	sign_up_user: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();

		const newUserData = {
			// Basic Info (Required)
			// employee_id: formData.get('employee_id'),
			username: formData.get('username'),
			password: formData.get('password'),
			confirm_password: formData.get('confirm_password'),
			name: formData.get('name'),
			first_name: formData.get('first_name'),
			last_name: formData.get('last_name'),
			// department: formData.get('department'),
			// role: formData.get('role'),
			is_active: formData.get('is_active'),
			is_staff: formData.get('is_staff'),
			is_superuser: formData.get('is_superuser'),

			// Contact Info (Optional)
			personal_phone: formData.get('personal_phone'),
			work_phone: formData.get('work_phone'),
			personal_email: formData.get('personal_email'),
			work_email: formData.get('work_email'),

			// Preferences (Optional)
			preferred_language: formData.get('preferred_language'),

			// Emergency Contact (Optional)
			emergency_contact_name: formData.get('emergency_contact_name'),
			emergency_contact_phone: formData.get('emergency_contact_phone'),
			emergency_contact_email: formData.get('emergency_contact_email')
		};

		// TODO - Delete this
		// console.log(formData)
		// console.log(`newUserData - ${JSON.stringify(newUserData)}`)

		const response = await services.users.signUpNewUser(newUserData, access_token);
		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},
	update_user: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();

		const userId = formData.get('id');
		const userData = {
			username: formData.get('username'),
			name: formData.get('name'),
			work_email: formData.get('work_email'),
			employee_id: formData.get('employee_id'),
			first_name: formData.get('first_name'),
			last_name: formData.get('last_name'),
			// department: formData.get('department'),
			// role: formData.get('role'),
			is_active: formData.get('is_active')
		};

		const response = await services.users.putById(userId, userData, access_token);
		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	delete_user: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();

		const userId = formData.get('id');

		const userData = {
			username: formData.get('username'),
			confirm_username: formData.get('confirm_username')
		};

		// // TODO - verify matching usernames
		// if (userData.username !== userData.confirm_username) {
		//     return fail(500, { error: `Usernames do not match` });
		// }

		const response = await services.users.deleteById(userId, userData, access_token);
		if (response.error_msg) {
			// return fail(500, { error: `${response.error_msg}` });
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	reactivate_user: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();

		const userId = formData.get('id');

		const userData = {
			username: formData.get('username'),
			confirm_username: formData.get('confirm_username')
		};

		// // TODO - verify matching usernames
		// if (userData.username !== userData.confirm_username) {
		//     return fail(500, { error: `Usernames do not match` });
		// }

		const response = await services.users.reactivateById(userId, userData, access_token);
		if (response.error_msg) {
			// return fail(500, { error: `${response.error_msg}` });
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	assign_user_department: async ({ request, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();
		const userId = formData.get('id');

		// Get all department IDs from the form data
		const departmentIds = formData.get('department_ids[]');
		// Convert the received value to an array of numbers
		const departmentIds_num = departmentIds
			.toString()
			.split(',')
			.map((num) => parseInt(num.trim()))
			.filter((num) => !isNaN(num));

		const departmentData = {
			department_ids: departmentIds_num
		};

		const response = await services.users.assignDepartmentById(
			userId,
			departmentData,
			access_token
		);

		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	assign_user_partner: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();
		const userId = formData.get('id');

		// Get all partner IDs from the form data
		const partnerIds = formData.get('partner_ids[]');
		const partnerIds_num = partnerIds
			.toString()
			.split(',')
			.map((num) => parseInt(num.trim()))
			.filter((num) => !isNaN(num));

		const partnerData = {
			// company_ids: partnerIds_num,
			partner_ids: partnerIds_num
		};

		// const response = await services.users.assignPartnerById(userId, partnerData, access_token)
		// const response = await services.companies.assignCompanyById(userId, partnerData, access_token)
		const response = await services.users.assignCompanyById(userId, partnerData, access_token);

		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	remove_user_partner: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();
		const userId = formData.get('id');

		// Get all partner IDs from the form data
		const partnerIds = formData.get('partner_ids[]');
		const partnerIds_num = partnerIds
			.toString()
			.split(',')
			.map((num) => parseInt(num.trim()));

		const partnerData = {
			company_ids: partnerIds_num
		};

		// const response = await services.users.deleteById(userId, userData, access_token)
		// const response = await services.users.removePartnerById(userId, partnerData, access_token)
		const response = await services.companies.removeCompanyById(userId, partnerData, access_token);
		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	assign_user_role: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();
		const userId = formData.get('id');

		// Get all role IDs from the form data
		const roleIds = formData.get('role_ids[]');
		// const roleIds_num = roleIds.toString().split(',').map(num => parseInt(num.trim()));

		const roleData = {
			role_ids: [roleIds]
		};

		const response = await services.users.assignRoleById(userId, roleData, access_token);
		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	remove_user_role: async ({ request, fetch, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();
		const userId = formData.get('id');

		// Get all role IDs from the form data
		const roleIds = formData.get('role_ids[]');
		const roleIds_num = roleIds
			.toString()
			.split(',')
			.map((num) => parseInt(num.trim()));

		const roleData = {
			role_ids: roleIds_num
		};

		const response = await services.users.removeRoleById(userId, roleData, access_token);
		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	update_user_line_id: async ({ request, fetch, cookies }) => {
		try {
			const access_token = cookies.get('access_token');
			if (!access_token) {
				return fail(401, { error: 'Unauthorized: No access token found' });
			}

			const formData = await request.formData();
			const userLineId = formData.get('userLineId') as string;
			const userId = formData.get('id') as string;

			if (!userLineId || !userId) {
				return fail(400, { error: 'Bad Request: Missing userLineId or userId' });
			}

			console.log('Updating User Line ID:', userLineId);

			const lineIdData = { line_user_id: userLineId };

			const response = await services.users.updateUserLineAccountId(
				userId,
				lineIdData,
				access_token
			);

			if (response.error_msg) {
				return fail(response.res_status, { error: response.error_msg });
			}

			return { success: true, res_msg: response.res_msg };
		} catch (error) {
			console.error('Error in update_user_line_id:', error);
			return fail(500, { error: 'Internal Server Error: Unable to update user line ID' });
		}
	},

	assign_user_tag: async ({ request, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();
		const userId = formData.get('id');

		// Get all tag IDs from the form data
		const tagIds = formData.get('tag_ids[]');
		// Convert the received value to an array of numbers
		const tagIds_num = tagIds
			.toString()
			.split(',')
			.map((num) => parseInt(num.trim()))
			.filter((num) => !isNaN(num));

		const tagData = {
			tag_ids: tagIds_num
		};

		const response = await services.users.assignTagsById(userId, tagData, access_token);

		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}
		let res_msg = response.res_msg;

		return { success: true, res_msg };
	},

	force_change_password: async ({ request, cookies }) => {
		const access_token = cookies.get('access_token');
		const formData = await request.formData();

		const userId = formData.get('user_id');
		const newPassword = formData.get('new_password');
		const confirmPassword = formData.get('confirm_password');

		// Validate passwords match
		if (newPassword !== confirmPassword) {
			return fail(400, { error: 'Passwords do not match' });
		}

		const passwordData = {
			new_password: newPassword,
			confirm_password: confirmPassword
		};

		const response = await services.users.forceChangePassword(userId, passwordData, access_token);

		if (response.error_msg) {
			return fail(response.res_status, { error: `${response.error_msg}` });
		}

		return {
			success: true,
			res_msg: response.res_msg || 'Password changed successfully'
		};
	}
};
